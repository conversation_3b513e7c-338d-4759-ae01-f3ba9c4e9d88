/* CreateNews Component Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.pageHeader {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pageTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #6b7280;
  font-size: 1rem;
}

.formContainer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.formSection {
  padding: 2rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.required {
  color: #ef4444;
}

.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  resize: vertical;
  min-height: 120px;
}

.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.contentTextarea {
  min-height: 300px;
}

.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.fileInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.fileInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.thumbnailPreview {
  margin-top: 1rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.previewImage {
  width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.settingsRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  padding-top: 2rem;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  color: #3b82f6;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.checkboxLabel {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.errorMessage {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.submitError {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.submitErrorText {
  color: #dc2626;
  font-size: 0.875rem;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.cancelButton {
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  color: #374151;
  border-radius: 8px;
  background: white;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.cancelButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.submitButton {
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.submitButton:hover {
  background: #2563eb;
}

.submitButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #9ca3af;
}

.loadingSpinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .formSection {
    padding: 1.5rem;
  }
  
  .settingsRow {
    grid-template-columns: 1fr;
  }
  
  .actionButtons {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .cancelButton,
  .submitButton {
    width: 100%;
    justify-content: center;
  }
}

/* Focus styles for accessibility */
.input:focus-visible,
.textarea:focus-visible,
.select:focus-visible,
.fileInput:focus-visible,
.checkbox:focus-visible,
.cancelButton:focus-visible,
.submitButton:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
