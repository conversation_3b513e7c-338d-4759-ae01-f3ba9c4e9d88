/* News page specific styles */
.newsPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

/* Search and Filter Styles */
.searchAndFilter {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.searchForm {
  margin-bottom: 1.5rem;
}

.searchInputGroup {
  position: relative;
  max-width: 500px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 4rem 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.searchButtons {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 0.25rem;
}

.searchButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.searchButton:hover {
  color: #667eea;
}

.clearButton {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.clearButton:hover {
  color: #ef4444;
}

.categoryFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.categoryBtn {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  color: #6b7280;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryBtn:hover {
  border-color: #667eea;
  color: #667eea;
}

.categoryBtn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.searchResults {
  background: #f3f4f6;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.searchResults p {
  margin: 0;
  color: #4b5563;
}

.noResults {
  color: #9ca3af !important;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .searchAndFilter {
    padding: 1rem;
  }

  .searchInputGroup {
    max-width: 100%;
  }

  .categoryFilter {
    justify-content: center;
  }

  .categoryBtn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.createNewsBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.createNewsBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.createNewsBtn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}

.contentArea {
  /* Main content styles */
}

.sidebar {
  /* Sidebar styles */
}

/* Loading, Error, and Empty States */
.loadingContainer,
.errorContainer,
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  margin: 2rem auto;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer p,
.errorContainer p,
.emptyContainer p {
  color: #666;
  margin: 0.5rem 0;
}

.errorContainer h3,
.emptyContainer h3 {
  color: #333;
  margin-bottom: 1rem;
}

.retryButton {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background: #5a67d8;
}

.featuredSection {
  margin-bottom: 3rem;
}

.featuredTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.featuredCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.featuredCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.featuredImage {
  width: 100%;
  height: 16rem;
  object-fit: cover;
}

.featuredContent {
  padding: 1.5rem;
}

.categoryTags {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.categoryTag {
  background: #dbeafe;
  color: #2563eb;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.newsDate {
  color: #6b7280;
  font-size: 0.875rem;
}

.featuredNewsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
}

.featuredNewsTitle:hover {
  color: #2563eb;
}

.newsExcerpt {
  color: #4b5563;
  line-height: 1.625;
  margin-bottom: 1rem;
}

.readMoreBtn {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.readMoreBtn:hover {
  color: #1d4ed8;
}

.filterSection {
  margin-bottom: 2rem;
}

.filterButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filterBtn {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.filterBtn.active {
  background: #2563eb;
  color: white;
}

.filterBtn:not(.active) {
  background: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.filterBtn:not(.active):hover {
  background: #eff6ff;
  color: #2563eb;
}

.newsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.newsCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.newsCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.newsImage {
  width: 100%;
  height: 12rem;
  object-fit: cover;
}

.newsContent {
  padding: 1.5rem;
}

.newsTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0.75rem 0;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.newsTitle:hover {
  color: #2563eb;
}

.newsCardExcerpt {
  color: #4b5563;
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.paginationButtons {
  display: flex;
  gap: 0.5rem;
}

.paginationBtn {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paginationBtn:hover {
  background: #f9fafb;
}

.paginationBtn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.sidebarCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
}

.searchInput:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchButton {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.3s ease;
}

.searchButton:hover {
  color: #2563eb;
}

.popularNews {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.popularItem {
  display: flex;
  gap: 0.75rem;
}

.popularImage {
  width: 4rem;
  height: 4rem;
  object-fit: cover;
  border-radius: 0.25rem;
  flex-shrink: 0;
}

.popularContent {
  flex: 1;
}

.popularTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.popularTitle:hover {
  color: #2563eb;
}

.popularDate {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .newsGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
    flex-direction: column;
    align-items: flex-start;
  }

  .createNewsBtn {
    width: 100%;
    justify-content: center;
  }
  
  .mainContent {
    padding: 1.5rem 1rem;
  }
  
  .newsGrid {
    grid-template-columns: 1fr;
  }
  
  .filterButtons {
    justify-content: center;
  }
  
  .pagination {
    overflow-x: auto;
    padding: 0 1rem;
  }
}

/* News Link Styles */
.newsLink {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.newsLink:hover {
  color: #3b82f6;
}

.featuredNewsTitle .newsLink:hover {
  color: #1d4ed8;
}

.popularTitle .newsLink {
  font-size: inherit;
  font-weight: inherit;
}

.popularTitle .newsLink:hover {
  color: #3b82f6;
}
