const express = require('express');
const router = express.Router();

const controller = require("../../controllers/admin/dashboard.controller");
const { authenticateToken, requireAdmin } = require("../../middleware/auth.middleware");

// T<PERSON>t cả routes đều cần xác thực và quyền admin
router.use(authenticateToken);
router.use(requireAdmin);

// [GET] /admin/dashboard/overview - Lấy tổng quan dashboard (stats + activities)
router.get("/overview", controller.getOverview);

// [GET] /admin/dashboard/stats - <PERSON><PERSON><PERSON> thống kê tổng quan
router.get("/stats", controller.getStats);

// [GET] /admin/dashboard/recent-activities - Lấy hoạt động gần đây
router.get("/recent-activities", controller.getRecentActivities);

module.exports = router;
